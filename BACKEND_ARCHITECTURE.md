# Waybill Backend Architecture & Implementation Guide

## 🏗️ **COMPREHENSIVE BACKEND ARCHITECTURE**

### 🚀 **1. Backend Framework Setup**

#### **Recommended: Node.js with Express.js + TypeScript**

```bash
# Project Setup
mkdir waybill-backend
cd waybill-backend
npm init -y

# Core Dependencies
npm install express cors helmet morgan compression
npm install jsonwebtoken bcryptjs
npm install prisma @prisma/client
npm install socket.io
npm install stripe
npm install firebase-admin
npm install nodemailer
npm install multer cloudinary
npm install joi express-rate-limit

# Development Dependencies
npm install -D typescript @types/node @types/express
npm install -D nodemon ts-node
npm install -D @types/jsonwebtoken @types/bcryptjs
npm install -D @types/cors @types/morgan
```

#### **Project Structure**
```
waybill-backend/
├── src/
│   ├── controllers/
│   │   ├── auth.controller.ts
│   │   ├── user.controller.ts
│   │   ├── order.controller.ts
│   │   ├── payment.controller.ts
│   │   └── notification.controller.ts
│   ├── middleware/
│   │   ├── auth.middleware.ts
│   │   ├── validation.middleware.ts
│   │   └── error.middleware.ts
│   ├── models/
│   │   └── index.ts
│   ├── routes/
│   │   ├── auth.routes.ts
│   │   ├── user.routes.ts
│   │   ├── order.routes.ts
│   │   └── payment.routes.ts
│   ├── services/
│   │   ├── auth.service.ts
│   │   ├── order.service.ts
│   │   ├── payment.service.ts
│   │   ├── notification.service.ts
│   │   └── socket.service.ts
│   ├── utils/
│   │   ├── jwt.util.ts
│   │   ├── email.util.ts
│   │   └── upload.util.ts
│   ├── config/
│   │   ├── database.ts
│   │   ├── firebase.ts
│   │   └── cloudinary.ts
│   └── app.ts
├── prisma/
│   ├── schema.prisma
│   └── migrations/
├── uploads/
└── package.json
```

---

### 🗄️ **2. Database Schema (PostgreSQL with Prisma)**

#### **Prisma Schema Setup**

```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String    @id @default(cuid())
  email             String    @unique
  password          String
  firstName         String
  lastName          String
  phone             String?
  profileImage      String?
  isEmailVerified   Boolean   @default(false)
  isPhoneVerified   Boolean   @default(false)
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  
  // Relations
  orders            Order[]
  addresses         Address[]
  paymentMethods    PaymentMethod[]
  notifications     Notification[]
  
  @@map("users")
}

model Address {
  id              String  @id @default(cuid())
  userId          String
  type            String  // 'home', 'work', 'other'
  label           String?
  street          String
  apartment       String?
  city            String
  state           String
  zipCode         String
  country         String  @default("US")
  latitude        Float?
  longitude       Float?
  isDefault       Boolean @default(false)
  
  // Relations
  user            User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  pickupOrders    Order[] @relation("PickupAddress")
  deliveryOrders  Order[] @relation("DeliveryAddress")
  
  @@map("addresses")
}

model Order {
  id                String      @id @default(cuid())
  userId            String
  status            OrderStatus @default(PENDING)
  
  // Addresses
  pickupAddressId   String
  deliveryAddressId String
  
  // Package Details
  packageSize       String
  packageWeight     Float?
  packageType       String
  packageDescription String?
  specialInstructions String?
  
  // Recipient
  recipientName     String
  recipientPhone    String
  
  // Delivery Options
  deliveryType      String      // 'standard', 'express', 'scheduled'
  scheduledTime     DateTime?
  estimatedDelivery DateTime?
  actualDelivery    DateTime?
  
  // Pricing
  basePrice         Float
  serviceFee        Float
  tax               Float
  totalPrice        Float
  
  // Driver Assignment
  driverId          String?
  
  // Tracking
  trackingNumber    String      @unique
  
  // Timestamps
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt
  
  // Relations
  user              User        @relation(fields: [userId], references: [id])
  pickupAddress     Address     @relation("PickupAddress", fields: [pickupAddressId], references: [id])
  deliveryAddress   Address     @relation("DeliveryAddress", fields: [deliveryAddressId], references: [id])
  driver            Driver?     @relation(fields: [driverId], references: [id])
  timeline          OrderTimeline[]
  payment           Payment?
  
  @@map("orders")
}

model Driver {
  id              String    @id @default(cuid())
  email           String    @unique
  firstName       String
  lastName        String
  phone           String
  profileImage    String?
  vehicleType     String
  vehiclePlate    String
  rating          Float     @default(5.0)
  isActive        Boolean   @default(true)
  currentLat      Float?
  currentLng      Float?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  
  // Relations
  orders          Order[]
  
  @@map("drivers")
}

model OrderTimeline {
  id          String    @id @default(cuid())
  orderId     String
  status      String
  title       String
  description String
  timestamp   DateTime  @default(now())
  completed   Boolean   @default(true)
  
  // Relations
  order       Order     @relation(fields: [orderId], references: [id], onDelete: Cascade)
  
  @@map("order_timeline")
}

model PaymentMethod {
  id              String    @id @default(cuid())
  userId          String
  type            String    // 'card', 'paypal', 'apple_pay'
  provider        String    // 'stripe', 'paypal'
  externalId      String    // Stripe payment method ID
  last4           String?
  brand           String?   // 'visa', 'mastercard', etc.
  expiryMonth     Int?
  expiryYear      Int?
  isDefault       Boolean   @default(false)
  createdAt       DateTime  @default(now())
  
  // Relations
  user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  payments        Payment[]
  
  @@map("payment_methods")
}

model Payment {
  id                String        @id @default(cuid())
  orderId           String        @unique
  paymentMethodId   String
  amount            Float
  currency          String        @default("USD")
  status            PaymentStatus @default(PENDING)
  stripePaymentId   String?
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt
  
  // Relations
  order             Order         @relation(fields: [orderId], references: [id])
  paymentMethod     PaymentMethod @relation(fields: [paymentMethodId], references: [id])
  
  @@map("payments")
}

model Notification {
  id          String            @id @default(cuid())
  userId      String
  type        NotificationType
  title       String
  message     String
  data        Json?
  isRead      Boolean           @default(false)
  createdAt   DateTime          @default(now())
  
  // Relations
  user        User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("notifications")
}

enum OrderStatus {
  PENDING
  CONFIRMED
  DRIVER_ASSIGNED
  PICKED_UP
  IN_TRANSIT
  DELIVERED
  CANCELLED
}

enum PaymentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  REFUNDED
}

enum NotificationType {
  ORDER_UPDATE
  DRIVER_ASSIGNED
  PICKUP_COMPLETE
  DELIVERY_COMPLETE
  PAYMENT_SUCCESS
  PROMOTION
}
```

#### **Database Setup Commands**

```bash
# Initialize Prisma
npx prisma init

# Generate Prisma Client
npx prisma generate

# Run migrations
npx prisma migrate dev --name init

# Seed database (optional)
npx prisma db seed
```

---

### 🔐 **3. Authentication System Implementation**

#### **JWT Authentication Service**

```typescript
// src/services/auth.service.ts
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export class AuthService {
  static async register(userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phone?: string;
  }) {
    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { email: userData.email }
    });

    if (existingUser) {
      throw new Error('User already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(userData.password, 12);

    // Create user
    const user = await prisma.user.create({
      data: {
        ...userData,
        password: hashedPassword,
      },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        phone: true,
        profileImage: true,
        createdAt: true,
      }
    });

    // Generate tokens
    const accessToken = this.generateAccessToken(user.id);
    const refreshToken = this.generateRefreshToken(user.id);

    return { user, accessToken, refreshToken };
  }

  static async login(email: string, password: string) {
    // Find user
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        password: true,
        firstName: true,
        lastName: true,
        phone: true,
        profileImage: true,
      }
    });

    if (!user) {
      throw new Error('Invalid credentials');
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      throw new Error('Invalid credentials');
    }

    // Generate tokens
    const accessToken = this.generateAccessToken(user.id);
    const refreshToken = this.generateRefreshToken(user.id);

    // Remove password from response
    const { password: _, ...userWithoutPassword } = user;

    return { user: userWithoutPassword, accessToken, refreshToken };
  }

  static generateAccessToken(userId: string): string {
    return jwt.sign(
      { userId },
      process.env.JWT_ACCESS_SECRET!,
      { expiresIn: '15m' }
    );
  }

  static generateRefreshToken(userId: string): string {
    return jwt.sign(
      { userId },
      process.env.JWT_REFRESH_SECRET!,
      { expiresIn: '7d' }
    );
  }

  static verifyAccessToken(token: string): { userId: string } {
    return jwt.verify(token, process.env.JWT_ACCESS_SECRET!) as { userId: string };
  }

  static verifyRefreshToken(token: string): { userId: string } {
    return jwt.verify(token, process.env.JWT_REFRESH_SECRET!) as { userId: string };
  }
}
```

#### **Authentication Middleware**

```typescript
// src/middleware/auth.middleware.ts
import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/auth.service';

export interface AuthRequest extends Request {
  userId?: string;
}

export const authenticateToken = (
  req: AuthRequest,
  res: Response,
  next: NextFunction
) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  try {
    const decoded = AuthService.verifyAccessToken(token);
    req.userId = decoded.userId;
    next();
  } catch (error) {
    return res.status(403).json({ error: 'Invalid or expired token' });
  }
};
```

---

### 🔄 **4. Real-time Implementation with Socket.io**

#### **Socket Service Setup**

```typescript
// src/services/socket.service.ts
import { Server as SocketServer } from 'socket.io';
import { Server } from 'http';
import { AuthService } from './auth.service';

export class SocketService {
  private io: SocketServer;
  private userSockets: Map<string, string> = new Map();

  constructor(server: Server) {
    this.io = new SocketServer(server, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      }
    });

    this.setupSocketAuth();
    this.setupEventHandlers();
  }

  private setupSocketAuth() {
    this.io.use((socket, next) => {
      const token = socket.handshake.auth.token;
      
      try {
        const decoded = AuthService.verifyAccessToken(token);
        socket.userId = decoded.userId;
        next();
      } catch (error) {
        next(new Error('Authentication error'));
      }
    });
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log(`User ${socket.userId} connected`);
      
      // Store user socket mapping
      this.userSockets.set(socket.userId, socket.id);

      // Join user to their personal room
      socket.join(`user:${socket.userId}`);

      socket.on('disconnect', () => {
        console.log(`User ${socket.userId} disconnected`);
        this.userSockets.delete(socket.userId);
      });
    });
  }

  // Send order update to specific user
  sendOrderUpdate(userId: string, orderData: any) {
    this.io.to(`user:${userId}`).emit('orderUpdate', orderData);
  }

  // Send driver location update
  sendDriverLocationUpdate(orderId: string, location: { lat: number; lng: number }) {
    this.io.to(`order:${orderId}`).emit('driverLocationUpdate', location);
  }

  // Broadcast to all connected users
  broadcast(event: string, data: any) {
    this.io.emit(event, data);
  }
}
```

---

### 💳 **5. Payment Integration with Stripe**

#### **Payment Service Implementation**

```typescript
// src/services/payment.service.ts
import Stripe from 'stripe';
import { PrismaClient } from '@prisma/client';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});

const prisma = new PrismaClient();

export class PaymentService {
  static async createPaymentMethod(userId: string, paymentMethodId: string) {
    // Attach payment method to customer
    const user = await prisma.user.findUnique({ where: { id: userId } });
    
    let customerId = user?.stripeCustomerId;
    
    if (!customerId) {
      // Create Stripe customer
      const customer = await stripe.customers.create({
        email: user?.email,
        name: `${user?.firstName} ${user?.lastName}`,
      });
      
      customerId = customer.id;
      
      // Update user with customer ID
      await prisma.user.update({
        where: { id: userId },
        data: { stripeCustomerId: customerId }
      });
    }

    // Attach payment method
    await stripe.paymentMethods.attach(paymentMethodId, {
      customer: customerId,
    });

    // Get payment method details
    const paymentMethod = await stripe.paymentMethods.retrieve(paymentMethodId);

    // Save to database
    const savedPaymentMethod = await prisma.paymentMethod.create({
      data: {
        userId,
        type: 'card',
        provider: 'stripe',
        externalId: paymentMethodId,
        last4: paymentMethod.card?.last4,
        brand: paymentMethod.card?.brand,
        expiryMonth: paymentMethod.card?.exp_month,
        expiryYear: paymentMethod.card?.exp_year,
      }
    });

    return savedPaymentMethod;
  }

  static async processPayment(orderId: string, paymentMethodId: string) {
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: { user: true }
    });

    if (!order) {
      throw new Error('Order not found');
    }

    try {
      // Create payment intent
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(order.totalPrice * 100), // Convert to cents
        currency: 'usd',
        payment_method: paymentMethodId,
        customer: order.user.stripeCustomerId,
        confirm: true,
        return_url: `${process.env.FRONTEND_URL}/payment/success`,
      });

      // Create payment record
      const payment = await prisma.payment.create({
        data: {
          orderId,
          paymentMethodId,
          amount: order.totalPrice,
          currency: 'USD',
          status: 'PROCESSING',
          stripePaymentId: paymentIntent.id,
        }
      });

      // Update payment status based on Stripe response
      if (paymentIntent.status === 'succeeded') {
        await prisma.payment.update({
          where: { id: payment.id },
          data: { status: 'COMPLETED' }
        });

        // Update order status
        await prisma.order.update({
          where: { id: orderId },
          data: { status: 'CONFIRMED' }
        });
      }

      return { payment, paymentIntent };
    } catch (error) {
      // Update payment status to failed
      await prisma.payment.create({
        data: {
          orderId,
          paymentMethodId,
          amount: order.totalPrice,
          currency: 'USD',
          status: 'FAILED',
        }
      });

      throw error;
    }
  }
}
```

---

### 📱 **6. Push Notifications with Firebase**

#### **Firebase Setup**

```typescript
// src/config/firebase.ts
import admin from 'firebase-admin';

const serviceAccount = require('../../firebase-service-account.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
});

export const messaging = admin.messaging();
```

#### **Notification Service**

```typescript
// src/services/notification.service.ts
import { messaging } from '../config/firebase';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export class NotificationService {
  static async sendOrderNotification(
    userId: string,
    title: string,
    message: string,
    data?: any
  ) {
    try {
      // Get user's FCM token
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { fcmToken: true }
      });

      if (!user?.fcmToken) {
        console.log('No FCM token found for user:', userId);
        return;
      }

      // Send push notification
      const response = await messaging.send({
        token: user.fcmToken,
        notification: {
          title,
          body: message,
        },
        data: data ? JSON.stringify(data) : undefined,
        android: {
          notification: {
            icon: 'ic_notification',
            color: '#2563EB',
          }
        },
        apns: {
          payload: {
            aps: {
              badge: 1,
              sound: 'default',
            }
          }
        }
      });

      // Save notification to database
      await prisma.notification.create({
        data: {
          userId,
          type: 'ORDER_UPDATE',
          title,
          message,
          data: data || {},
        }
      });

      console.log('Notification sent successfully:', response);
    } catch (error) {
      console.error('Error sending notification:', error);
    }
  }

  static async sendBulkNotification(
    userIds: string[],
    title: string,
    message: string
  ) {
    const users = await prisma.user.findMany({
      where: { id: { in: userIds } },
      select: { id: true, fcmToken: true }
    });

    const tokens = users
      .filter(user => user.fcmToken)
      .map(user => user.fcmToken!);

    if (tokens.length === 0) {
      console.log('No FCM tokens found for bulk notification');
      return;
    }

    try {
      const response = await messaging.sendMulticast({
        tokens,
        notification: {
          title,
          body: message,
        },
      });

      console.log('Bulk notification sent:', response);
    } catch (error) {
      console.error('Error sending bulk notification:', error);
    }
  }
}
```

---

### 📁 **7. File Upload with Cloudinary**

#### **Upload Service**

```typescript
// src/services/upload.service.ts
import { v2 as cloudinary } from 'cloudinary';
import multer from 'multer';

cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

export const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  },
});

export class UploadService {
  static async uploadImage(
    buffer: Buffer,
    folder: string = 'waybill'
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      cloudinary.uploader.upload_stream(
        {
          folder,
          resource_type: 'image',
          transformation: [
            { width: 500, height: 500, crop: 'limit' },
            { quality: 'auto' },
            { format: 'auto' }
          ]
        },
        (error, result) => {
          if (error) {
            reject(error);
          } else {
            resolve(result!.secure_url);
          }
        }
      ).end(buffer);
    });
  }
}
```

---

### 🛣️ **8. API Routes Structure**

#### **Main App Setup**

```typescript
// src/app.ts
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';

import authRoutes from './routes/auth.routes';
import userRoutes from './routes/user.routes';
import orderRoutes from './routes/order.routes';
import paymentRoutes from './routes/payment.routes';

const app = express();

// Security middleware
app.use(helmet());
app.use(cors());
app.use(compression());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});
app.use(limiter);

// Logging
app.use(morgan('combined'));

// Body parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/payments', paymentRoutes);

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

export default app;
```

#### **Environment Variables**

```env
# .env
DATABASE_URL="postgresql://username:password@localhost:5432/waybill"
JWT_ACCESS_SECRET="your-super-secret-access-key"
JWT_REFRESH_SECRET="your-super-secret-refresh-key"
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_PUBLISHABLE_KEY="pk_test_..."
CLOUDINARY_CLOUD_NAME="your-cloud-name"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"
FIREBASE_PROJECT_ID="your-project-id"
EMAIL_HOST="smtp.gmail.com"
EMAIL_PORT=587
EMAIL_USER="<EMAIL>"
EMAIL_PASS="your-app-password"
FRONTEND_URL="http://localhost:3000"
PORT=5000
```

---

## 🚀 **DEPLOYMENT RECOMMENDATIONS**

### **Production Infrastructure**
- **Backend**: Railway, Heroku, or AWS EC2
- **Database**: AWS RDS PostgreSQL or Supabase
- **File Storage**: Cloudinary or AWS S3
- **Real-time**: Socket.io with Redis adapter
- **Monitoring**: Sentry for error tracking
- **CI/CD**: GitHub Actions or GitLab CI

### **Performance Optimizations**
- Database indexing on frequently queried fields
- Redis caching for session management
- CDN for static assets
- Database connection pooling
- API response compression

This comprehensive backend architecture provides a solid foundation for the Waybill delivery app with scalability, security, and real-time capabilities! 🚀

---

## 📋 **API ENDPOINTS REFERENCE**

### **Authentication Endpoints**
```
POST /api/auth/register
POST /api/auth/login
POST /api/auth/refresh
POST /api/auth/logout
POST /api/auth/forgot-password
POST /api/auth/reset-password
```

### **User Management**
```
GET /api/users/profile
PUT /api/users/profile
POST /api/users/upload-avatar
GET /api/users/addresses
POST /api/users/addresses
PUT /api/users/addresses/:id
DELETE /api/users/addresses/:id
```

### **Order Management**
```
POST /api/orders
GET /api/orders
GET /api/orders/:id
PUT /api/orders/:id/status
DELETE /api/orders/:id
GET /api/orders/:id/timeline
POST /api/orders/:id/timeline
```

### **Payment Processing**
```
POST /api/payments/methods
GET /api/payments/methods
DELETE /api/payments/methods/:id
POST /api/payments/process
GET /api/payments/history
```

### **Real-time Events**
```
orderUpdate - Order status changes
driverLocationUpdate - Driver location updates
paymentSuccess - Payment completion
notificationReceived - New notifications
```
