# Waybill Delivery App - Supabase Backend Setup

This guide will help you set up the complete Supabase backend for the Waybill delivery app.

## Prerequisites

- Supabase account (free tier available)
- Node.js and npm installed
- Expo CLI installed

## 1. Create Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign up/login
2. Click "New Project"
3. Choose your organization
4. Enter project details:
   - Name: `waybill-delivery`
   - Database Password: (generate a strong password)
   - Region: Choose closest to your users
5. Click "Create new project"
6. Wait for the project to be ready (2-3 minutes)

## 2. Database Setup

### Step 1: Run Schema Creation

1. Go to your Supabase dashboard
2. Navigate to "SQL Editor"
3. Copy and paste the contents of `supabase/schema.sql`
4. Click "Run" to create all tables and types

### Step 2: Run Security Policies

1. In the SQL Editor, create a new query
2. Copy and paste the contents of `supabase/policies.sql`
3. Click "Run" to set up Row Level Security

### Step 3: Create Database Functions

Add these additional functions to your database:

```sql
-- Function to get nearby drivers
CREATE OR REPLACE FUNCTION get_nearby_drivers(lat FLOAT, lng FLOAT, radius_km FLOAT DEFAULT 10)
RETURNS TABLE (
  id UUID,
  first_name TEXT,
  last_name TEXT,
  rating DECIMAL,
  vehicle_type TEXT,
  distance_km FLOAT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    d.id,
    d.first_name,
    d.last_name,
    d.rating,
    d.vehicle_type,
    (6371 * acos(cos(radians(lat)) * cos(radians(d.current_lat)) * 
     cos(radians(d.current_lng) - radians(lng)) + 
     sin(radians(lat)) * sin(radians(d.current_lat)))) AS distance_km
  FROM public.drivers d
  WHERE d.is_active = true 
    AND d.is_available = true
    AND d.current_lat IS NOT NULL 
    AND d.current_lng IS NOT NULL
    AND (6371 * acos(cos(radians(lat)) * cos(radians(d.current_lat)) * 
         cos(radians(d.current_lng) - radians(lng)) + 
         sin(radians(lat)) * sin(radians(d.current_lat)))) <= radius_km
  ORDER BY distance_km
  LIMIT 10;
END;
$$ LANGUAGE plpgsql;
```

## 3. Storage Setup

### Create Storage Buckets

1. Go to "Storage" in your Supabase dashboard
2. Create the following buckets:

#### Profile Images Bucket
- Name: `profile-images`
- Public: `true`
- File size limit: `5MB`
- Allowed MIME types: `image/jpeg,image/png,image/webp`

#### Order Documents Bucket (optional)
- Name: `order-documents`
- Public: `false`
- File size limit: `10MB`
- Allowed MIME types: `image/jpeg,image/png,application/pdf`

### Storage Policies

Add these policies for the storage buckets:

```sql
-- Profile images policies
CREATE POLICY "Users can upload own profile images" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'profile-images' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can update own profile images" ON storage.objects
  FOR UPDATE USING (bucket_id = 'profile-images' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Profile images are publicly viewable" ON storage.objects
  FOR SELECT USING (bucket_id = 'profile-images');
```

## 4. Authentication Setup

### Email Templates

1. Go to "Authentication" > "Email Templates"
2. Customize the email templates:

#### Confirm Signup Template
```html
<h2>Welcome to Waybill!</h2>
<p>Thank you for signing up. Please confirm your email address by clicking the link below:</p>
<p><a href="{{ .ConfirmationURL }}">Confirm your email</a></p>
<p>If you didn't create an account with us, please ignore this email.</p>
```

#### Reset Password Template
```html
<h2>Reset your Waybill password</h2>
<p>Someone requested a password reset for your account. If this was you, click the link below:</p>
<p><a href="{{ .ConfirmationURL }}">Reset your password</a></p>
<p>If you didn't request this, please ignore this email.</p>
```

### Auth Settings

1. Go to "Authentication" > "Settings"
2. Configure:
   - Site URL: `waybill://` (for deep linking)
   - Redirect URLs: Add your app's redirect URLs
   - Email confirmation: `Enabled`
   - Double confirm email changes: `Enabled`

## 5. Environment Configuration

1. Copy `.env.example` to `.env`
2. Fill in your Supabase credentials:

```env
EXPO_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

You can find these values in:
- Supabase Dashboard > Settings > API

## 6. Real-time Setup

### Enable Real-time

1. Go to "Database" > "Replication"
2. Enable real-time for these tables:
   - `orders`
   - `order_timeline`
   - `driver_locations`
   - `notifications`

### Real-time Policies

```sql
-- Allow users to subscribe to their own orders
ALTER PUBLICATION supabase_realtime ADD TABLE orders;
ALTER PUBLICATION supabase_realtime ADD TABLE order_timeline;
ALTER PUBLICATION supabase_realtime ADD TABLE notifications;
ALTER PUBLICATION supabase_realtime ADD TABLE driver_locations;
```

## 7. Test Data (Optional)

Add some test data to verify everything works:

```sql
-- Insert test driver
INSERT INTO public.drivers (email, first_name, last_name, phone, vehicle_type, vehicle_plate, current_lat, current_lng)
VALUES ('<EMAIL>', 'John', 'Driver', '+1234567890', 'Car', 'ABC123', 40.7128, -74.0060);

-- Insert test user (after signing up through the app)
-- This will be created automatically when users sign up
```

## 8. Production Considerations

### Security Checklist

- [ ] Review all RLS policies
- [ ] Set up database backups
- [ ] Configure rate limiting
- [ ] Set up monitoring and alerts
- [ ] Review storage policies
- [ ] Enable database logs

### Performance Optimization

- [ ] Add appropriate indexes
- [ ] Set up connection pooling
- [ ] Configure caching
- [ ] Monitor query performance

### Monitoring

1. Go to "Reports" in Supabase dashboard
2. Monitor:
   - API usage
   - Database performance
   - Storage usage
   - Authentication metrics

## 9. Integration with App

Once your Supabase backend is set up:

1. Update your `.env` file with the correct values
2. Test authentication by signing up a new user
3. Test order creation and real-time updates
4. Verify push notifications work
5. Test all CRUD operations

## 10. Troubleshooting

### Common Issues

**Authentication not working:**
- Check your Supabase URL and anon key
- Verify RLS policies are correct
- Check email confirmation settings

**Real-time not working:**
- Ensure tables are added to replication
- Check real-time policies
- Verify subscription filters

**Storage uploads failing:**
- Check bucket policies
- Verify file size limits
- Check MIME type restrictions

### Support

- Supabase Documentation: https://supabase.com/docs
- Supabase Discord: https://discord.supabase.com
- GitHub Issues: Create issues in your project repository

## Next Steps

After completing the Supabase setup:

1. Test all app functionality
2. Set up additional integrations (Stripe, Mapbox)
3. Configure push notifications
4. Deploy to app stores
5. Set up monitoring and analytics

Your Waybill delivery app backend is now ready for production! 🚀
