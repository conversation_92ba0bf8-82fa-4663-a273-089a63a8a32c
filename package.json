{"name": "waybill", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/bottom-tabs": "^7.4.5", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.5", "@stripe/stripe-react-native": "0.45.0", "@supabase/supabase-js": "^2.53.0", "expo": "~53.0.20", "expo-auth-session": "^6.2.1", "expo-constants": "^17.1.7", "expo-crypto": "^14.1.5", "expo-device": "^7.1.4", "expo-font": "^13.3.2", "expo-linear-gradient": "^14.1.5", "expo-local-authentication": "^16.0.5", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.5", "react-native-maps": "1.20.1", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-vector-icons": "^10.3.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}