# Waybill Delivery App - Environment Variables
# Copy this file to .env and fill in your actual values

# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=https://fglmbpcurroruwqetngp.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZnbG1icGN1cnJvcnV3cWV0bmdwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQzOTkyNDgsImV4cCI6MjA2OTk3NTI0OH0.PjinV-JbegHr0bveFqdBqrnfiRhjaNUmudUxDXkdTTg

# Mapbox Configuration (for address geocoding and maps)
EXPO_PUBLIC_MAPBOX_TOKEN=your_mapbox_access_token

# Stripe Configuration (for payments)
EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key

# App Configuration
EXPO_PUBLIC_APP_ENV=development
EXPO_PUBLIC_API_BASE_URL=https://your-api-domain.com

# Firebase Configuration (for push notifications - optional if using Expo)
EXPO_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your_firebase_auth_domain
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your_firebase_project_id
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your_firebase_storage_bucket
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_firebase_messaging_sender_id
EXPO_PUBLIC_FIREBASE_APP_ID=your_firebase_app_id
