# Push Notifications Setup Guide - Waybill Delivery App

## 🔔 **Production-Ready Push Notifications Implementation**

This guide provides step-by-step instructions for setting up real push notifications using Firebase Cloud Messaging (FCM) and Expo's notification service.

---

## 📋 **Prerequisites**

- Firebase project created
- Expo account and project configured
- Physical device for testing (notifications don't work in simulators)
- Supabase project set up

---

## 🔥 **1. Firebase Setup**

### **1.1 Create Firebase Project**
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or select existing project
3. Enable Google Analytics (optional)
4. Wait for project creation to complete

### **1.2 Add Android App**
1. Click "Add app" → Android icon
2. **Android package name**: `com.yourcompany.waybill` (match your app.json)
3. **App nickname**: `Waybill Delivery`
4. Download `google-services.json`
5. Place file in your project root: `Waybill/google-services.json`

### **1.3 Add iOS App**
1. Click "Add app" → iOS icon
2. **iOS bundle ID**: `com.yourcompany.waybill` (match your app.json)
3. **App nickname**: `Waybill Delivery`
4. Download `GoogleService-Info.plist`
5. Place file in your project root: `Waybill/GoogleService-Info.plist`

### **1.4 Enable Cloud Messaging**
1. Go to **Project Settings** → **Cloud Messaging**
2. Note your **Server Key** and **Sender ID**
3. For iOS: Upload your APNs certificate or key

---

## ⚙️ **2. Expo Configuration**

### **2.1 Update app.json**
```json
{
  "expo": {
    "name": "Waybill",
    "slug": "waybill-delivery",
    "version": "1.0.0",
    "orientation": "portrait",
    "icon": "./assets/icon.png",
    "userInterfaceStyle": "automatic",
    "splash": {
      "image": "./assets/splash.png",
      "resizeMode": "contain",
      "backgroundColor": "#ffffff"
    },
    "assetBundlePatterns": [
      "**/*"
    ],
    "ios": {
      "supportsTablet": true,
      "bundleIdentifier": "com.yourcompany.waybill",
      "googleServicesFile": "./GoogleService-Info.plist"
    },
    "android": {
      "adaptiveIcon": {
        "foregroundImage": "./assets/adaptive-icon.png",
        "backgroundColor": "#FFFFFF"
      },
      "package": "com.yourcompany.waybill",
      "googleServicesFile": "./google-services.json"
    },
    "web": {
      "favicon": "./assets/favicon.png"
    },
    "plugins": [
      [
        "expo-notifications",
        {
          "icon": "./assets/notification-icon.png",
          "color": "#ffffff",
          "defaultChannel": "default"
        }
      ]
    ],
    "extra": {
      "eas": {
        "projectId": "your-expo-project-id"
      }
    }
  }
}
```

### **2.2 Install Required Dependencies**
```bash
# Install notification dependencies
npx expo install expo-notifications expo-device expo-constants

# Install Firebase (optional, for advanced features)
npm install @react-native-firebase/app @react-native-firebase/messaging
```

### **2.3 Update Environment Variables**
Add to your `.env` file:
```env
# Firebase Configuration
EXPO_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
EXPO_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
EXPO_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
EXPO_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
EXPO_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
EXPO_PUBLIC_FIREBASE_APP_ID=your_app_id

# Expo Project ID
EXPO_PUBLIC_EAS_PROJECT_ID=your_expo_project_id
```

---

## 🔧 **3. Code Implementation**

### **3.1 Notification Permissions Component**
Create `src/components/NotificationPermissions.js`:
```javascript
import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Alert } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import NotificationService from '../services/notificationService';

const NotificationPermissions = ({ onPermissionGranted }) => {
  const [permissionStatus, setPermissionStatus] = useState('unknown');

  useEffect(() => {
    checkPermissionStatus();
  }, []);

  const checkPermissionStatus = async () => {
    const result = await NotificationService.checkPermissions();
    setPermissionStatus(result.status);
  };

  const requestPermissions = async () => {
    const result = await NotificationService.initializePushNotifications();
    
    if (result.success) {
      setPermissionStatus('granted');
      onPermissionGranted?.(result.token);
    } else {
      Alert.alert(
        'Notification Permissions',
        'Please enable notifications in your device settings to receive order updates.',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Settings', onPress: () => Linking.openSettings() }
        ]
      );
    }
  };

  if (permissionStatus === 'granted') {
    return null; // Don't show if already granted
  }

  return (
    <View style={styles.container}>
      <Ionicons name="notifications-outline" size={48} color="#007AFF" />
      <Text style={styles.title}>Stay Updated</Text>
      <Text style={styles.description}>
        Get real-time updates about your deliveries
      </Text>
      <TouchableOpacity style={styles.button} onPress={requestPermissions}>
        <Text style={styles.buttonText}>Enable Notifications</Text>
      </TouchableOpacity>
    </View>
  );
};
```

### **3.2 Update AuthContext for Notifications**
Add to `src/context/AuthContext.js`:
```javascript
// After successful login
const result = await login(email, password);
if (result.success) {
  // Initialize notifications
  const notificationResult = await NotificationService.initializePushNotifications();
  if (notificationResult.success) {
    await NotificationService.savePushToken(result.user.id, notificationResult.token);
  }
}
```

---

## 🧪 **4. Testing Push Notifications**

### **4.1 Development Testing**
```bash
# Start the app
npm start

# Test on physical device
# Scan QR code with Expo Go
# Grant notification permissions when prompted
# Check console for push token
```

### **4.2 Send Test Notification**
Use Expo's push notification tool:
```bash
# Install Expo CLI
npm install -g @expo/cli

# Send test notification
expo push:android:upload --api-key YOUR_FCM_SERVER_KEY
expo push:ios:upload --api-key YOUR_APNS_KEY
```

### **4.3 Production Testing**
```bash
# Build production app
eas build --platform all

# Install on device and test
# Notifications should work without Expo Go
```

---

## 🚀 **5. Production Deployment**

### **5.1 EAS Build Configuration**
Create `eas.json`:
```json
{
  "cli": {
    "version": ">= 3.0.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal"
    },
    "preview": {
      "distribution": "internal"
    },
    "production": {
      "autoIncrement": true
    }
  },
  "submit": {
    "production": {}
  }
}
```

### **5.2 Build Commands**
```bash
# Build for production
eas build --platform all --profile production

# Submit to stores
eas submit --platform all
```

---

## 📱 **6. Notification Features Implemented**

### **✅ Core Features:**
- **Permission handling** with user-friendly prompts
- **Token management** with Supabase storage
- **Order notifications** for status updates
- **Real-time delivery** updates
- **Cross-platform support** (iOS and Android)

### **✅ Notification Types:**
- Order confirmed
- Driver assigned
- Package picked up
- Out for delivery
- Package delivered
- Order cancelled

### **✅ User Experience:**
- **Opt-in permissions** with clear benefits
- **Settings management** to enable/disable notifications
- **Rich notifications** with order details
- **Deep linking** to relevant app screens

---

## 🔍 **7. Troubleshooting**

### **Common Issues:**
1. **No token received**: Check device permissions and Firebase config
2. **Notifications not appearing**: Verify FCM server key and app bundle ID
3. **iOS notifications failing**: Check APNs certificate and bundle ID
4. **Android notifications not working**: Verify google-services.json placement

### **Debug Commands:**
```bash
# Check notification permissions
adb shell dumpsys notification

# View device logs
npx react-native log-android
npx react-native log-ios
```

---

## ✅ **Implementation Complete**

Your Waybill app now has production-ready push notifications with:
- ✅ **Firebase Cloud Messaging** integration
- ✅ **Proper permission handling** with user consent
- ✅ **Cross-platform support** for iOS and Android
- ✅ **Real-time order updates** via notifications
- ✅ **Production deployment** ready configuration

**Next: Test notifications on physical devices and deploy to production!** 🚀
